// Brightness manager utility - no React hooks to prevent unnecessary re-renders

const createBrightnessManager = () => {
  const state = {
    participantBrightness: new Map(),
    rpcRegistered: false,
    unregisterRpc: null,
    debounceTimer: null,
    cleanupTimer: null,
    room: null,
    currentBrightness: 100,
  };

  const handleRpcMessage = (data) => {
    try {
      const payload = JSON.parse(data.payload);
      const { brightness } = payload;
      state.participantBrightness.set(data.callerIdentity, brightness);
      return "Brightness updated successfully";
    } catch (error) {
      console.error('❌ Error processing brightness RPC:', error);
      return "Error: Failed to update brightness";
    }
  };

  const registerRpcMethod = () => {
    if (!state.room || state.rpcRegistered) {
      return;
    }

    if (state.room.state !== "connected" || !state.room.localParticipant) {
      console.warn('⚠️ Cannot register brightness RPC: room not connected or local participant unavailable');
      return;
    }

    try {
      state.unregisterRpc = state.room.localParticipant.registerRpcMethod(
        "setBrightness",
        handleRpcMessage
      );

      state.rpcRegistered = true;
    } catch (error) {
      console.error('❌ Failed to register brightness RPC method:', error);
    }
  };

  const sendToParticipant = async (participant, brightness) => {
    try {
      if (!state.room || !state.room.localParticipant) {
        throw new Error('Room or local participant not available');
      }

      if (!participant || !participant.identity) {
        throw new Error('Participant identity is missing');
      }

      if (state.room.state !== "connected") {
        throw new Error(`Room not connected (state: ${state.room.state})`);
      }

      const payload = JSON.stringify({
        brightness,
        participantId: state.room.localParticipant.identity,
        timestamp: Date.now()
      });

      const response = await state.room.localParticipant.performRpc({
        destinationIdentity: participant.identity,
        method: "setBrightness",
        payload,
        responseTimeout: 5000
      });

      return { participant: participant.identity, success: true, response };
    } catch (error) {
      console.error(`❌ Failed to send brightness to ${participant?.identity || 'unknown'}:`, error.message);
      return { participant: participant?.identity || 'unknown', success: false, error: error.message };
    }
  };

  const createBatches = (array, batchSize) => {
    const batches = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  };

  const executeBrightnessSend = async (brightness, maxConcurrent) => {
    // Validate room and connection state
    if (!state.room || state.room.state !== "connected" || !state.room.localParticipant) {
      console.warn('⚠️ Cannot send brightness: room not connected or local participant unavailable');
      return;
    }

    if (brightness === 100) {
      return;
    }

    const remoteParticipants = Array.from(state.room.remoteParticipants.values());

    if (remoteParticipants.length === 0) {
      return;
    }
    const batches = createBatches(remoteParticipants, maxConcurrent);

    for (const batch of batches)  {
      const promises = batch.map(participant => sendToParticipant(participant, brightness));
      // eslint-disable-next-line no-await-in-loop
      await Promise.allSettled(promises);

      if (batches.length > 1) {
        // eslint-disable-next-line no-await-in-loop
        await new Promise(resolve => {
          setTimeout(resolve, 100);
        });
      }
    }
  };

  const cleanupDisconnectedParticipants = () => {
    if (!state.room) return;

    const connectedParticipants = new Set(
      Array.from(state.room.remoteParticipants.keys())
    );

    for (const participantId of state.participantBrightness.keys()) {
      if (!connectedParticipants.has(participantId)) {
        state.participantBrightness.delete(participantId);
      }
    }
  };

  const handleParticipantDisconnected = (participant) => {
    if (participant && participant.identity) {
      // Clean up brightness data for the disconnected REMOTE participant
      // Note: This only triggers for remote participants, not local participant
      state.participantBrightness.delete(participant.identity);
      console.log(`🧹 Cleaned up brightness data for disconnected participant: ${participant.identity}`);
    }
  };

  const handleLocalParticipantDisconnected = () => {
    console.log('🧹 Local participant disconnected - performing complete cleanup');
    // When WE (local participant) disconnect, clean up everything
    state.participantBrightness.clear();
  };

  const setupParticipantEventListeners = () => {
    if (!state.room) return;

    // Clean up when REMOTE participants disconnect
    state.room.on('participantDisconnected', handleParticipantDisconnected);

    state.room.on('disconnected', handleLocalParticipantDisconnected);

    if (!state.cleanupTimer) {
      state.cleanupTimer = setInterval(() => {
        cleanupDisconnectedParticipants();
      }, 300000); // Every 5 minutes as fallback
    }
  };

  const removeParticipantEventListeners = () => {
    if (state.room) {
      state.room.off('participantDisconnected', handleParticipantDisconnected);
      state.room.off('disconnected', handleLocalParticipantDisconnected);
    }
  };


  return {
    get participantBrightness() { return state.participantBrightness; },
    get rpcRegistered() { return state.rpcRegistered; },
    get room() { return state.room; },
    get currentBrightness() { return state.currentBrightness; },

    initialize(room) {
      if (!room || state.room === room) return;

      state.room = room;
      registerRpcMethod();
      setupParticipantEventListeners();
    },

    sendBrightnessToAll(brightness, options = {}) {
      const {
        debounceMs = 1000,
        skipIfSame = true,
        maxConcurrent = 10
      } = options;

      if (state.debounceTimer) {
        clearTimeout(state.debounceTimer);
      }

      if (skipIfSame && state.currentBrightness === brightness) {
        return;
      }

      state.currentBrightness = brightness;

      state.debounceTimer = setTimeout(() => {
        executeBrightnessSend(brightness, maxConcurrent);
      }, debounceMs);
    },

    sendToNewParticipant(participant, brightness = state.currentBrightness) {
      // Validate inputs
      if (!participant || !participant.identity) {
        console.warn('⚠️ Cannot send brightness: invalid participant');
        return;
      }

      if (brightness === 100) {
        return;
      }

      if (!state.room || state.room.state !== "connected" || !state.room.localParticipant) {
        console.warn(`⚠️ Cannot send brightness to ${participant.identity}: room not ready`);
        return;
      }

      setTimeout(() => {
        if (state.room && state.room.state === "connected" && state.room.localParticipant) {
          sendToParticipant(participant, brightness);
        } else {
          console.warn(`⚠️ Room state changed, skipping brightness send to ${participant.identity}`);
        }
      }, 3000);
    },

    getParticipantBrightness(participantId) {
      return state.participantBrightness.get(participantId) || 100;
    },

    getAllParticipantBrightness() {
      return new Map(state.participantBrightness);
    },

    cleanup() {
      // Clear timers
      if (state.debounceTimer) {
        clearTimeout(state.debounceTimer);
        state.debounceTimer = null;
      }

      if (state.cleanupTimer) {
        clearInterval(state.cleanupTimer);
        state.cleanupTimer = null;
      }

      removeParticipantEventListeners();

      if (state.unregisterRpc && state.rpcRegistered) {
        try {
          state.unregisterRpc();
        } catch (error) {
          console.error('❌ Error unregistering brightness RPC method:', error);
        }
      }

      state.participantBrightness.clear();
      state.rpcRegistered = false;
      state.unregisterRpc = null;
      state.room = null;
      state.currentBrightness = 100;
    },

    handleRpcMessage,
    registerRpcMethod
  };
};

const brightnessManager = createBrightnessManager();

// Optimized brightness manager functions for direct use
export const initializeBrightnessManager = (room) => {
  if (room) {
    brightnessManager.initialize(room);
  }
};

export const sendBrightnessToAll = (brightness, options = {}) => {
  brightnessManager.sendBrightnessToAll(brightness, {
    debounceMs: 500,
    skipIfSame: true,
    maxConcurrent: 10,
    ...options
  });
};

export const sendBrightnessToNewParticipant = (participant, brightness) => {
  brightnessManager.sendToNewParticipant(participant, brightness);
};

export const getParticipantBrightness = (participantId) => {
  return brightnessManager.getParticipantBrightness(participantId);
};

export const getAllParticipantBrightness = () => {
  return brightnessManager.getAllParticipantBrightness();
};

export const cleanupBrightnessManager = () => {
  brightnessManager.cleanup();
};

export default brightnessManager;
