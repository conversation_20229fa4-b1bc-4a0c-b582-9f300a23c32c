// Brightness manager utility - stateless functions like virtualBackground.js

// Simple state storage (no complex patterns)
let participantBrightness = new Map();
let rpcRegistered = false;
let unregisterRpc = null;
let debounceTimer = null;
let currentRoom = null;

const handleRpcMessage = (data) => {
  try {
    const payload = JSON.parse(data.payload);
    const { brightness } = payload;
    console.log(`📥 [BRIGHTNESS] Received brightness ${brightness} from participant:`, data.callerIdentity);
    participantBrightness.set(data.callerIdentity, brightness);
    return "Brightness updated successfully";
  } catch (error) {
    console.error('❌ Error processing brightness RPC:', error);
    return "Error: Failed to update brightness";
  }
};

// Initialize brightness RPC for a room (like toggleBlur function)
export async function initializeBrightnessRPC(room) {
  if (!room || room.state !== 'connected') {
    console.warn('⚠️ [BRIGHTNESS] Cannot initialize: room not connected');
    return;
  }

  // Prevent double registration
  if (currentRoom === room && rpcRegistered) {
    console.log('🔄 [BRIGHTNESS] RPC already registered for this room');
    return;
  }

  try {
    console.log('🔧 [BRIGHTNESS] Registering RPC for room:', room.name);
    
    // Cleanup previous registration if different room
    if (currentRoom && currentRoom !== room && unregisterRpc) {
      console.log('🧹 [BRIGHTNESS] Cleaning up previous RPC registration');
      unregisterRpc();
      rpcRegistered = false;
    }

    // Register RPC method
    unregisterRpc = room.localParticipant.registerRpcMethod(
      'setBrightness',
      handleRpcMessage
    );
    
    rpcRegistered = true;
    currentRoom = room;
    console.log('✅ [BRIGHTNESS] RPC method registered successfully');

    // Setup participant event listeners
    const handleParticipantDisconnected = (participant) => {
      console.log(`👋 [BRIGHTNESS] Participant ${participant.identity} disconnected, removing brightness data`);
      participantBrightness.delete(participant.identity);
    };

    const handleRoomDisconnected = () => {
      console.log('🔌 [BRIGHTNESS] Room disconnected, clearing all brightness data');
      participantBrightness.clear();
    };

    room.on('participantDisconnected', handleParticipantDisconnected);
    room.on('disconnected', handleRoomDisconnected);

  } catch (error) {
    console.error('❌ [BRIGHTNESS] Failed to register RPC method:', error);
    rpcRegistered = false;
  }
}

// Send brightness to all participants (like toggleBlur function)
export async function sendBrightnessToAll(room, brightness) {
  if (!room || room.state !== 'connected') {
    console.warn('⚠️ [BRIGHTNESS] Cannot send: room not connected');
    return;
  }

  if (brightness === 100) {
    console.log('🔆 [BRIGHTNESS] Brightness is 100, skipping send');
    return;
  }

  // Clear existing debounce timer
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }

  // Debounce the send operation
  debounceTimer = setTimeout(async () => {
    try {
      const remoteParticipants = Array.from(room.remoteParticipants.values());
      console.log(`📡 [BRIGHTNESS] Sending brightness ${brightness} to ${remoteParticipants.length} participants`);

      if (remoteParticipants.length === 0) {
        console.log('👥 [BRIGHTNESS] No remote participants to send brightness to');
        return;
      }

      // Send to all participants
      const sendPromises = remoteParticipants.map(async (participant) => {
        try {
          console.log(`📤 [BRIGHTNESS] Sending brightness ${brightness} to participant: ${participant.identity}`);
          const response = await room.localParticipant.performRpc({
            destinationIdentity: participant.identity,
            method: 'setBrightness',
            payload: JSON.stringify({ brightness }),
            responseTimeout: 5000,
          });
          console.log(`✅ [BRIGHTNESS] Successfully sent brightness to ${participant.identity}:`, response);
          return { participant: participant.identity, success: true, response };
        } catch (error) {
          console.error(`❌ [BRIGHTNESS] Failed to send brightness to ${participant.identity}:`, error);
          return { participant: participant.identity, success: false, error: error.message };
        }
      });

      await Promise.all(sendPromises);
    } catch (error) {
      console.error('❌ [BRIGHTNESS] Error in brightness send operation:', error);
    }
  }, 100); // 100ms debounce
}

// Send brightness to a specific new participant (like toggleBlur function)
export async function sendBrightnessToNewParticipant(room, participant, brightness) {
  if (!room || room.state !== 'connected') {
    console.warn('⚠️ [BRIGHTNESS] Cannot send: room not connected');
    return;
  }

  if (!participant || !participant.identity) {
    console.warn('⚠️ [BRIGHTNESS] Cannot send: invalid participant');
    return;
  }

  if (brightness === 100) {
    return; // No need to send default brightness
  }

  // Wait a bit for participant to be ready
  setTimeout(async () => {
    try {
      console.log(`📤 [BRIGHTNESS] Sending brightness ${brightness} to new participant: ${participant.identity}`);
      const response = await room.localParticipant.performRpc({
        destinationIdentity: participant.identity,
        method: 'setBrightness',
        payload: JSON.stringify({ brightness }),
        responseTimeout: 5000,
      });
      console.log(`✅ [BRIGHTNESS] Successfully sent brightness to new participant ${participant.identity}:`, response);
    } catch (error) {
      console.error(`❌ [BRIGHTNESS] Failed to send brightness to new participant ${participant.identity}:`, error);
    }
  }, 3000);
}

// Get all participant brightness values (simple getter)
export function getAllParticipantBrightness() {
  return new Map(participantBrightness);
}

// Cleanup brightness RPC (like noEffect function)
export function cleanupBrightnessRPC() {
  console.log('🧹 [BRIGHTNESS] Cleaning up brightness RPC');
  
  if (unregisterRpc) {
    try {
      unregisterRpc();
      console.log('✅ [BRIGHTNESS] RPC method unregistered successfully');
    } catch (error) {
      console.error('❌ [BRIGHTNESS] Error unregistering RPC method:', error);
    }
    unregisterRpc = null;
  }

  if (debounceTimer) {
    clearTimeout(debounceTimer);
    debounceTimer = null;
  }

  rpcRegistered = false;
  currentRoom = null;
  participantBrightness.clear();
  console.log('✅ [BRIGHTNESS] Cleanup completed');
}
