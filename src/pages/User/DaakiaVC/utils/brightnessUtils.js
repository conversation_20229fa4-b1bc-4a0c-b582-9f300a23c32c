// Brightness manager utility - stateless functions like virtualBackground.js

// Simple state storage (no complex patterns)
let participantBrightness = new Map();
let rpcRegistered = false;
let debounceTimer = null;
let currentRoom = null;

const handleRpcMessage = (data) => {
  try {
    const payload = JSON.parse(data.payload);
    const { brightness } = payload;
    participantBrightness.set(data.callerIdentity, brightness);
    return "Brightness updated successfully";
  } catch (error) {
    console.error('Brightness RPC processing failed:', error.message);
    return "Error: Failed to update brightness";
  }
};

// Initialize brightness RPC for a room (like toggleBlur function)
export async function initializeBrightnessRPC(room) {
  if (!room || room.state !== 'connected') {
    return;
  }

  // Prevent double registration
  if (currentRoom === room && rpcRegistered) {
    return;
  }

  try {
    // IMPORTANT: Always unregister first to prevent "overriding RPC handler" error
    // This is required by LiveKit - must call unregisterRpcMethod before registerRpcMethod
    try {
      room.localParticipant.unregisterRpcMethod('setBrightness');
    } catch (error) {
      // This is expected if no method was registered before
    }

    // Now register the RPC method
    room.localParticipant.registerRpcMethod('setBrightness', handleRpcMessage);

    rpcRegistered = true;
    currentRoom = room;

    // Setup participant event listeners
    const handleParticipantDisconnected = (participant) => {
      participantBrightness.delete(participant.identity);
    };

    const handleRoomDisconnected = () => {
      participantBrightness.clear();
    };

    room.on('participantDisconnected', handleParticipantDisconnected);
    room.on('disconnected', handleRoomDisconnected);

  } catch (error) {
    console.error('Failed to register brightness RPC method:', error.message);
    rpcRegistered = false;
  }
}

// Send brightness to all participants (like toggleBlur function)
export async function sendBrightnessToAll(room, brightness) {
  if (!room || room.state !== 'connected') {
    return;
  }

  if (brightness === 100) {
    return;
  }

  // Clear existing debounce timer
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }

  // Debounce the send operation
  debounceTimer = setTimeout(async () => {
    try {
      const remoteParticipants = Array.from(room.remoteParticipants.values());

      if (remoteParticipants.length === 0) {
        return;
      }

      // Send to all participants
      const sendPromises = remoteParticipants.map(async (participant) => {
        try {
          const response = await room.localParticipant.performRpc({
            destinationIdentity: participant.identity,
            method: 'setBrightness',
            payload: JSON.stringify({ brightness }),
            responseTimeout: 5000,
          });
          return { participant: participant.identity, success: true, response };
        } catch (error) {
          console.error(`Failed to send brightness to ${participant.identity}:`, error.message);
          return { participant: participant.identity, success: false, error: error.message };
        }
      });

      await Promise.all(sendPromises);
    } catch (error) {
      console.error('Brightness send operation failed:', error.message);
    }
  }, 100); // 100ms debounce
}

// Send brightness to a specific new participant (like toggleBlur function)
export async function sendBrightnessToNewParticipant(room, participant, brightness) {
  if (!room || room.state !== 'connected') {
    return;
  }

  if (!participant || !participant.identity) {
    return;
  }

  if (brightness === 100) {
    return; // No need to send default brightness
  }

  // Wait a bit for participant to be ready
  setTimeout(async () => {
    try {
      await room.localParticipant.performRpc({
        destinationIdentity: participant.identity,
        method: 'setBrightness',
        payload: JSON.stringify({ brightness }),
        responseTimeout: 5000,
      });
    } catch (error) {
      console.error(`Failed to send brightness to new participant ${participant.identity}:`, error.message);
    }
  }, 3000);
}

// Get all participant brightness values (simple getter)
export function getAllParticipantBrightness() {
  return new Map(participantBrightness);
}

// Cleanup brightness RPC (like noEffect function)
export function cleanupBrightnessRPC() {
  if (currentRoom && rpcRegistered) {
    try {
      currentRoom.localParticipant.unregisterRpcMethod('setBrightness');
    } catch (error) {
      console.error('Failed to unregister brightness RPC method:', error.message);
    }
  }

  if (debounceTimer) {
    clearTimeout(debounceTimer);
    debounceTimer = null;
  }

  rpcRegistered = false;
  currentRoom = null;
  participantBrightness.clear();
}
